/PROG  PHOTO_TEST
/ATTR
COMMENT = "Test program for iRVision photo capture";
TCD:  STACK_SIZE = 0,
      TASK_PRIORITY = 50,
      TIME_SLICE = 0,
      BUSY_LAMP_OFF = 0,
      ABORT_REQUEST = 0,
      PAUSE_REQUEST = 0;
DEFAULT_GROUP = 1,1,*,*,*;
/MN
   ! ========================================
   ! iRVision Photo Capture Test Program
   ! ========================================
   ! This program demonstrates how to use the iRVision
   ! photo capture functionality
   
   ! Display test start message
   MESSAGE[Starting iRVision Photo Test]
   
   ! Start the iRVision program in background
   RUN IRVISION
   
   ! Wait for camera to initialize
   WAIT 2(sec)
   
   ! Check if camera is ready (R[10] should be 1)
   IF R[10]<>1 THEN
     MESSAGE[ERROR: Camera not ready]
     PAUSE
   ENDIF
   
   MESSAGE[Camera ready - Starting photo sequence]
   
   ! Take 3 test photos with 3 second intervals
   CNT[1]=0
   
   LBL[10] ! Photo loop
   CNT[1]=CNT[1]+1
   
   MESSAGE['Taking photo '+NUMTOSTR(CNT[1],0)+' of 3']
   
   ! Trigger photo capture by setting R[15]=1
   R[15]=1
   
   ! Wait for photo to complete (R[11] should become 2)
   LBL[20]
   IF R[11]=1 THEN
     ! Still capturing
     WAIT .1(sec)
     JMP LBL[20]
   ENDIF
   
   IF R[11]=2 THEN
     ! Photo captured successfully
     MESSAGE['Photo '+NUMTOSTR(CNT[1],0)+' captured successfully']
   ELSE
     ! Error occurred
     MESSAGE['Photo '+NUMTOSTR(CNT[1],0)+' failed - Error code: '+NUMTOSTR(R[14],0)]
   ENDIF
   
   ! Wait before next photo
   WAIT 3(sec)
   
   ! Continue loop if more photos needed
   IF CNT[1]<3 THEN
     JMP LBL[10]
   ENDIF
   
   MESSAGE[Photo test sequence completed]
   
   ! Optional: Display last photo information
   CALL IRVISION:600  ! Get last photo info
   MESSAGE['Last photo: '+SR[1]]
   
   ! Test complete
   MESSAGE[iRVision test completed successfully]

/END
