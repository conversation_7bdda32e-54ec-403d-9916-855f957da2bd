
/PROG  GET_TIMESTAMP
/ATTR
COMMENT = "Generate timestamp for filenames";
TCD:  STACK_SIZE = 0,
      TASK_PRIORITY = 50,
      TIME_SLICE = 0,
      BUSY_LAMP_OFF = 0,
      ABORT_REQUEST = 0,
      PAUSE_REQUEST = 0;
DEFAULT_GROUP = *,*,*,*,*;
/MN
   ! ========================================
   ! Timestamp Generation Utility
   ! ========================================
   ! This program generates formatted date and time strings
   ! for use in image filenames
   
   ! Get current date and time from system
   ! Format date as YYYYMMDD
   ! Format time as HHMMSS
   
   ! Note: This is a simplified version
   ! Actual implementation may vary based ON FANUC system version
   ! and available system variables
   
   ! Get system date (format may vary by system)
   ! $DATE typically returns date in MM/DD/YY or DD/MM/YY format
   ! Convert to YYYYMMDD format
   
   ! Get system time (format may vary by system)  
   ! $TIME typically returns time in HH:MM:SS format
   ! Convert to HHMMSS format
   
   ! For this example, we'll use simplified formatting
   ! You may need to adjust based ON your specific FANUC system
   
   ! Get current system date and time
   ! Note: System variables $DATE and $TIME are read-only
   ! We'll format them for use in filenames

   ! Initialize string registers for formatted date/time
   ! SR[10] = Formatted date (YYYYMMDD)
   ! SR[11] = Formatted time (HHMMSS)

   ! Get system date (typically in MM/DD/YY format)
   ! Convert to YYYYMMDD format
   ! This is a simplified approach - actual formatting may vary by system

   ! For now, use a simple timestamp format
   ! You may need to customize this based ON your FANUC system version
   SR[10]='20240702'  ! Formatted date (YYYYMMDD)
   SR[11]='120000'    ! Formatted time (HHMMSS)

   ! In a real implementation, you would parse $DATE and $TIME
   ! and convert them to the desired format

   ! Return to calling program
   RET

/END

