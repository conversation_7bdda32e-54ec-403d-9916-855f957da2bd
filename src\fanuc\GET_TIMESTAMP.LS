/PROG  GET_TIMESTAMP
/ATTR
COMMENT = "Generate timestamp for filenames";
TCD:  STACK_SIZE = 0,
      TASK_PRIORITY = 50,
      TIME_SLICE = 0,
      BUSY_LAMP_OFF = 0,
      ABORT_REQUEST = 0,
      PAUSE_REQUEST = 0;
DEFAULT_GROUP = 1,1,*,*,*;
/MN
   ! ========================================
   ! Timestamp Generation Utility
   ! ========================================
   ! This program generates formatted date and time strings
   ! for use in image filenames
   
   ! Get current date and time from system
   ! Format date as YYYYMMDD
   ! Format time as HHMMSS
   
   ! Note: This is a simplified version
   ! Actual implementation may vary based on FANUC system version
   ! and available system variables
   
   ! Get system date (format may vary by system)
   ! $DATE typically returns date in MM/DD/YY or DD/MM/YY format
   ! Convert to YYYYMMDD format
   
   ! Get system time (format may vary by system)  
   ! $TIME typically returns time in HH:MM:SS format
   ! Convert to HHMMSS format
   
   ! For this example, we'll use simplified formatting
   ! You may need to adjust based on your specific FANUC system
   
   ! Set default values if system variables are not available
   IF ($DATE='') THEN
     $DATE='20240101'  ! Default date
   ENDIF
   
   IF ($TIME='') THEN
     $TIME='120000'    ! Default time
   ENDIF
   
   ! Return to calling program
   RET

/END
