/PROG  GET_TIMESTAMP
/ATTR
OWNER       = USER;
PROG_SIZE   = 10;
CREATE      = DATE 24-06-20  TIME 12:00:00;
MODIFIED    = DATE 24-06-20  TIME 12:00:00;
FILE_NAME   = ;
VERSION     = 0;
LINE_COUNT  = 0;
MEMORY_SIZE = 10;
PROTECT     = READ_WRITE;
TCD:  STACK_SIZE = 0,
      TASK_PRIORITY = 50,
      TIME_SLICE = 0,
      BUSY_LAMP_OFF = 0,
      ABORT_REQUEST = 0,
      PAUSE_REQUEST = 0;
DEFAULT_GROUP = *,*,*,*,*;
CONTROL_CODE  = 00000000 00000000;
/MN
   1:  RET;
/END