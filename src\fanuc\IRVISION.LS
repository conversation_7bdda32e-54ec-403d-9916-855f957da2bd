/PROG  IRVISION
/ATTR
TCD:  ;
/MN
   R[10]=0
   R[11]=0
   R[12]=0
   R[13]=0
   R[14]=0
   SR[3]='VISION_PROC_1'
   MESSAGE[Camera Initialization...]
   LBL[100]
   VISION INIT
   IF (VISION_AVAILABLE()=FALSE) THEN
     MESSAGE[ERROR: Vision system not available]
     R[14]=1
     JMP LBL[900]
   ENDIF
   VISION LOAD SR[3]
   IF (VISION_PROCESS_LOADED(SR[3])=FALSE) THEN
     MESSAGE[ERROR: Failed to load vision process]
     R[14]=2
     JMP LBL[900]
   ENDIF
   R[10]=1
   MESSAGE[Camera Ready]
   LBL[200]
   IF DI[2]=ON THEN
     JMP LBL[300]
   ENDIF
   IF R[15]=1 THEN
     R[15]=0
     JMP LBL[300]
   ENDIF
   WAIT .1(sec)
   JMP LBL[200]
   LBL[300]
   R[11]=1
   MESSAGE[Capturing Photo...]
   R[12]=R[12]+1
   CALL GET_TIMESTAMP
   SR[1]='IMG_'+SR[10]+'_'+SR[11]+'_'+NUMTOSTR(R[12],3)+'.jpg'
   SR[2]='/vision/images/'+SR[1]
   DO[1]=ON
   WAIT .2(sec)
   VISION SNAP SR[3]
   IF (VISION_SNAP_STATUS()=FALSE) THEN
     MESSAGE[ERROR: Image capture failed]
     R[11]=3
     R[14]=3
     DO[1]=OFF
     JMP LBL[900]
   ENDIF
   VISION SAVE_IMAGE SR[2]
   IF (VISION_SAVE_STATUS()=FALSE) THEN
     MESSAGE[ERROR: Image save failed]
     R[11]=3
     R[14]=4
     DO[1]=OFF
     JMP LBL[900]
   ENDIF
   DO[1]=OFF
   R[11]=2
   MESSAGE['Photo saved: '+SR[1]]
   $FILENAME=SR[2]
   R[3]=1
   MESSAGE['Photo captured successfully']
   UALM[10]
   WAIT 1(sec)
   R[11]=0
   JMP LBL[200]
   LBL[400]
   R[15]=1
   RET
   LBL[500]
   RET
   LBL[600]
   RET
   LBL[900]
   DO[1]=OFF
   SELECT R[14]=1,JMP LBL[901]
          =2,JMP LBL[902]
          =3,JMP LBL[903]
          =4,JMP LBL[904]
          ELSE,JMP LBL[999]
   LBL[901]
   MESSAGE[ERROR 1: Vision system not available]
   UALM[21]
   JMP LBL[999]
   LBL[902]
   MESSAGE[ERROR 2: Vision process load failed]
   UALM[22]
   JMP LBL[999]
   LBL[903]
   MESSAGE[ERROR 3: Image capture failed]
   UALM[23]
   JMP LBL[999]
   LBL[904]
   MESSAGE[ERROR 4: Image save failed]
   UALM[24]
   JMP LBL[999]
   LBL[999]
   R[11]=3
   MESSAGE[iRVision program stopped due to error]
   WAIT DI[3]=ON
   R[14]=0
   R[11]=0
   JMP LBL[200]
/END
