# iRVision 2D Camera Photo Capture Setup Guide

## Overview
This guide explains how to set up and use the iRVision 2D camera photo capture system for your Fanuc robot.

## Files Created
- `iRVision.TP` - Main photo capture program
- `GET_TIMESTAMP.TP` - Timestamp utility for filename generation
- `PHOTO_TEST.TP` - Test program to verify functionality

## Prerequisites

### Hardware Requirements
1. Fanuc robot with iRVision option installed
2. 2D camera connected to the robot controller
3. Proper lighting setup (optional but recommended)
4. Network connection for image transfer (if using existing transfer system)

### Software Requirements
1. iRVision software option enabled on robot controller
2. Vision process configured and tested
3. Proper I/O configuration for camera triggers and lighting

## Configuration Steps

### 1. Vision Process Setup
Before using the photo capture program, you need to:

1. **Create a Vision Process:**
   - Use iRVision setup to create a vision process
   - Name it `VISION_PROC_1` (or update line 31 in iRVision.TP)
   - Configure camera settings (resolution, exposure, etc.)
   - Test the vision process manually

2. **Camera Calibration:**
   - Perform camera calibration if needed for your application
   - Set up proper lighting conditions

### 2. I/O Configuration
Configure the following I/O points in your robot controller:

**Digital Inputs:**
- `DI[2]` - External photo trigger (optional)
- `DI[3]` - Error reset button (optional)

**Digital Outputs:**
- `DO[1]` - Camera illumination control (optional)

### 3. Register Usage
The program uses the following registers:

**Numeric Registers:**
- `R[10]` - Camera status (0=not ready, 1=ready)
- `R[11]` - Photo capture status (0=idle, 1=capturing, 2=complete, 3=error)
- `R[12]` - Image counter for unique filenames
- `R[13]` - Vision process ID (reserved)
- `R[14]` - Error code
- `R[15]` - Program trigger (set to 1 to trigger photo)

**String Registers:**
- `SR[1]` - Base filename
- `SR[2]` - Full image path
- `SR[3]` - Vision process name

### 4. File Path Configuration
Update the image save path in line 115 of iRVision.TP:
```
SR[2]='/vision/images/'+SR[1]
```
Change `/vision/images/` to match your system's directory structure.

## Usage Instructions

### Method 1: External Trigger
1. Start the iRVision program
2. Connect a switch or sensor to DI[2]
3. When DI[2] turns ON, a photo will be captured automatically

### Method 2: Program Trigger
1. Start the iRVision program
2. From another program or manually, set R[15]=1
3. The photo will be captured automatically

### Method 3: Using the Test Program
1. Run the PHOTO_TEST program
2. It will automatically start iRVision and take 3 test photos
3. Monitor the messages for status updates

## Integration with Image Transfer System

The photo capture program integrates with your existing image transfer system:

1. **Automatic Integration:**
   - After capturing a photo, the filename is stored in `$FILENAME`
   - Register R[3] is set to 1 to signal image ready
   - Your existing IMGSEND program will detect this and transfer the image

2. **Manual Transfer:**
   - Uncomment line 166 in iRVision.TP to enable automatic transfer
   - Or manually run IMGSEND after photo capture

## Error Codes and Troubleshooting

### Error Codes
- **Error 1:** Vision system not available
  - Check iRVision option is enabled
  - Verify camera connection

- **Error 2:** Vision process load failed
  - Check vision process name in SR[3]
  - Verify process exists and is properly configured

- **Error 3:** Image capture failed
  - Check camera connection and settings
  - Verify lighting conditions
  - Check vision process configuration

- **Error 4:** Image save failed
  - Check file path permissions
  - Verify disk space availability
  - Check directory exists

### Common Issues

1. **Camera Not Detected:**
   - Verify camera cable connections
   - Check camera power supply
   - Restart robot controller if necessary

2. **Poor Image Quality:**
   - Adjust camera exposure settings
   - Improve lighting conditions
   - Clean camera lens

3. **File Save Errors:**
   - Check directory permissions
   - Verify available disk space
   - Ensure directory path exists

## Customization Options

### Filename Format
Current format: `IMG_YYYYMMDD_HHMMSS_NNN.jpg`
- Modify line 112 in iRVision.TP to change format
- Update GET_TIMESTAMP.TP for different date/time formatting

### Image Format
- Change `.jpg` to `.bmp` or other supported formats
- Update vision process settings for different image formats

### Trigger Methods
- Add additional trigger methods by modifying the main loop (LBL[200])
- Implement custom trigger logic as needed

## Testing and Validation

1. **Run PHOTO_TEST program** to verify basic functionality
2. **Check image quality** and adjust camera settings if needed
3. **Test image transfer** to ensure integration works properly
4. **Verify error handling** by simulating error conditions

## Maintenance

1. **Regular Calibration:** Recalibrate camera periodically
2. **Lens Cleaning:** Keep camera lens clean for optimal image quality
3. **Backup Configuration:** Save vision process and program backups
4. **Monitor Disk Space:** Ensure adequate storage for images

## Support

For additional support:
1. Refer to Fanuc iRVision documentation
2. Contact Fanuc technical support
3. Check robot controller alarm history for detailed error information
