
/PROG  IRVISION
/ATTR
COMMENT = "iRVision 2D Camera Photo Capture";
TCD:  STACK_SIZE = 0,
      TASK_PRIORITY = 50,
      TIME_SLICE = 0,
      BUSY_LAMP_OFF = 0,
      ABORT_REQUEST = 0,
      PAUSE_REQUEST = 0;
DEFAULT_GROUP = 1,1,*,*,*;
/MN
   ! ========================================
   ! iRVision 2D Camera Photo Capture Program
   ! ========================================
   ! This program captures photos using iRVision 2D camera
   ! and integrates with the image transfer system

   ! Initialize variables and registers
   R[10]=0    ! Camera status (0=not ready, 1=ready)
   R[11]=0    ! Photo capture status (0=idle, 1=capturing, 2=complete, 3=error)
   R[12]=0    ! Image counter for unique filenames
   R[13]=0    ! Vision process ID
   R[14]=0    ! Error code

   ! String registers for filenames and paths
   ! SR[1] = Base filename
   ! SR[2] = Full image path
   ! SR[3] = Vision process name

   ! Set vision process name (change this to match your setup)
   SR[3]='VISION_PROC_1'

   ! Display startup message
   MESSAGE[Camera Initialization...]

   ! ========================================
   ! CAMERA INITIALIZATION
   ! ========================================

   LBL[100] ! Camera Setup

   ! Initialize iRVision system
   VISION INIT

   ! Check if vision system is available
   IF (VISION_AVAILABLE()=FALSE) THEN
     MESSAGE[ERROR: Vision system not available]
     R[14]=1  ! Set error code
     JMP LBL[900] ! Jump to error handling
   ENDIF

   ! Load vision process
   VISION LOAD SR[3]

   ! Check if process loaded successfully
   IF (VISION_PROCESS_LOADED(SR[3])=FALSE) THEN
     MESSAGE[ERROR: Failed to load vision process]
     R[14]=2  ! Set error code
     JMP LBL[900] ! Jump to error handling
   ENDIF

   ! Set camera ready status
   R[10]=1
   MESSAGE[Camera Ready]

   ! ========================================
   ! MAIN PHOTO CAPTURE LOOP
   ! ========================================

   LBL[200] ! Main Loop

   ! Wait for photo capture trigger
   ! This can be triggered by:
   ! - Digital Input DI[2] (external trigger)
   ! - Register R[15]=1 (program trigger)
   ! - Manual trigger from teach pendant

   ! Check for external trigger
   IF DI[2]=ON THEN
     JMP LBL[300] ! Start photo capture
   ENDIF

   ! Check for program trigger
   IF R[15]=1 THEN
     R[15]=0  ! Reset trigger
     JMP LBL[300] ! Start photo capture
   ENDIF

   ! Check for manual trigger (F1 key on teach pendant)
   ! This would typically be set up through a menu or UI

   ! Wait and loop
   WAIT .1(sec)
   JMP LBL[200]

   ! ========================================
   ! PHOTO CAPTURE SEQUENCE
   ! ========================================

   LBL[300] ! Photo Capture

   ! Set capturing status
   R[11]=1
   MESSAGE[Capturing Photo...]

   ! Increment image counter for unique filename
   R[12]=R[12]+1

   ! Generate filename with timestamp
   ! Format: IMG_YYYYMMDD_HHMMSS_NNN.jpg
   CALL GET_TIMESTAMP
   SR[1]='IMG_'+SR[10]+'_'+SR[11]+'_'+NUMTOSTR(R[12],3)+'.jpg'

   ! Set full image path (adjust path as needed for your system)
   SR[2]='/vision/images/'+SR[1]

   ! Turn on illumination if available
   DO[1]=ON  ! Assuming DO[1] controls camera lighting

   ! Wait for illumination to stabilize
   WAIT .2(sec)

   ! Capture image using vision process
   VISION SNAP SR[3]

   ! Check if image capture was successful
   IF (VISION_SNAP_STATUS()=FALSE) THEN
     MESSAGE[ERROR: Image capture failed]
     R[11]=3  ! Set error status
     R[14]=3  ! Set error code
     DO[1]=OFF ! Turn off illumination
     JMP LBL[900] ! Jump to error handling
   ENDIF

   ! Save captured image to file
   VISION SAVE_IMAGE SR[2]

   ! Check if image save was successful
   IF (VISION_SAVE_STATUS()=FALSE) THEN
     MESSAGE[ERROR: Image save failed]
     R[11]=3  ! Set error status
     R[14]=4  ! Set error code
     DO[1]=OFF ! Turn off illumination
     JMP LBL[900] ! Jump to error handling
   ENDIF

   ! Turn off illumination
   DO[1]=OFF

   ! Set completion status
   R[11]=2
   MESSAGE['Photo saved: '+SR[1]]

   ! ========================================
   ! IMAGE TRANSFER INTEGRATION
   ! ========================================

   ! Set filename for image transfer system
   $FILENAME=SR[2]

   ! Signal image ready for transfer (used by IMGSEND program)
   R[3]=1

   ! Optional: Automatically trigger image transfer
   ! Uncomment the next line if you want automatic transfer
   ! RUN IMGSEND

   ! Display success message
   MESSAGE['Photo captured successfully']
   UALM[10] ! Success alarm

   ! Wait before next capture
   WAIT 1(sec)

   ! Reset status and return to main loop
   R[11]=0
   JMP LBL[200]

   ! ========================================
   ! UTILITY SUBROUTINES
   ! ========================================

   LBL[400] ! Manual Photo Trigger
   ! This label can be called from other programs
   ! or triggered manually
   R[15]=1  ! Set program trigger
   RET

   LBL[500] ! Get Camera Status
   ! Returns camera status in R[10]
   ! 0=not ready, 1=ready
   RET

   LBL[600] ! Get Last Photo Info
   ! Returns last photo filename in SR[1]
   ! and full path in SR[2]
   RET

   ! ========================================
   ! ERROR HANDLING
   ! ========================================

   LBL[900] ! Error Handler

   ! Turn off any outputs
   DO[1]=OFF  ! Turn off illumination

   ! Display error message based on error code
   SELECT R[14]=1,JMP LBL[901]
          =2,JMP LBL[902]
          =3,JMP LBL[903]
          =4,JMP LBL[904]
          ELSE,JMP LBL[999]

   LBL[901] ! Vision system not available
   MESSAGE[ERROR 1: Vision system not available]
   UALM[21]
   JMP LBL[999]

   LBL[902] ! Vision process load failed
   MESSAGE[ERROR 2: Vision process load failed]
   UALM[22]
   JMP LBL[999]

   LBL[903] ! Image capture failed
   MESSAGE[ERROR 3: Image capture failed]
   UALM[23]
   JMP LBL[999]

   LBL[904] ! Image save failed
   MESSAGE[ERROR 4: Image save failed]
   UALM[24]
   JMP LBL[999]

   LBL[999] ! General error exit
   R[11]=3  ! Set error status
   MESSAGE[iRVision program stopped due to error]

   ! Wait for acknowledgment or reset
   WAIT DI[3]=ON  ! Assuming DI[3] is error reset button

   ! Reset error status
   R[14]=0
   R[11]=0

   ! Return to main loop
   JMP LBL[200]

/END
